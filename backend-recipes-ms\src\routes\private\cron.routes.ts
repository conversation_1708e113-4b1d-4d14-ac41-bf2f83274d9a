import express, { Router } from "express";
import { StatusCodes } from "http-status-codes";
import { triggerManualRecipeCostFreshnessUpdate } from "../../cron/recipeCostFreshnessCron";
import { triggerManualIngredientCostUpdate } from "../../cron/ingredientCostUpdateCron";

const routes: Router = express.Router();

/**
 * @swagger
 * /v1/private/cron/recipe-cost-freshness:
 *   post:
 *     summary: Manually trigger recipe cost freshness update
 *     tags: [Cron Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organization_id:
 *                 type: string
 *                 description: Optional organization ID to process specific organization only
 *     responses:
 *       200:
 *         description: Recipe cost freshness update completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       500:
 *         description: Internal server error
 */
routes.post("/recipe-cost-freshness", async (req: any, res: any) => {
  try {
    const { organization_id } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.roles?.[0];

    // Only allow super admin or admin to trigger cron jobs
    if (!userRole || !['super_admin', 'admin'].includes(userRole)) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("INSUFFICIENT_PERMISSIONS"),
      });
    }

    console.log(`Manual recipe cost freshness update triggered by user ${userId} for organization: ${organization_id || 'all'}`);

    const result = await triggerManualRecipeCostFreshnessUpdate(organization_id);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_COST_FRESHNESS_UPDATE_COMPLETED"),
      data: result
    });

  } catch (error: any) {
    console.error("Error triggering manual recipe cost freshness update:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
      error: error.message
    });
  }
});

/**
 * @swagger
 * /v1/private/cron/ingredient-cost-update:
 *   post:
 *     summary: Manually trigger ingredient cost update
 *     tags: [Cron Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organization_id:
 *                 type: string
 *                 description: Optional organization ID to process specific organization only
 *     responses:
 *       200:
 *         description: Ingredient cost update completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       500:
 *         description: Internal server error
 */
routes.post("/ingredient-cost-update", async (req: any, res: any) => {
  try {
    const { organization_id } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.roles?.[0];

    // Only allow super admin or admin to trigger cron jobs
    if (!userRole || !['super_admin', 'admin'].includes(userRole)) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("INSUFFICIENT_PERMISSIONS"),
      });
    }

    console.log(`Manual ingredient cost update triggered by user ${userId} for organization: ${organization_id || 'all'}`);

    const result = await triggerManualIngredientCostUpdate(organization_id);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("INGREDIENT_COST_UPDATE_COMPLETED"),
      data: result
    });

  } catch (error: any) {
    console.error("Error triggering manual ingredient cost update:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR"),
      error: error.message
    });
  }
});

export default routes;
