import { areRecipeCostsOutdated } from "./timestamp.helper";
import costCalculationService from "../services/cost-calculation.service";
import { sequelize } from "../models";

/**
 * Check freshness and auto-update recipe costs if needed
 */
export const checkAndUpdateRecipeCosts = async (
  recipeId: number,
  organizationId: string,
  options: {
    forceUpdate?: boolean;
    autoUpdate?: boolean;
    transaction?: any;
  } = {}
): Promise<{
  wasOutdated: boolean;
  wasUpdated: boolean;
  totalCost: number;
  freshness: {
    isOutdated: boolean;
    outdatedIngredients: Array<{
      id: number;
      name: string;
      lastUpdated: Date;
    }>;
    lastUpdated: Date | null;
    autoUpdateEnabled: boolean;
  };
  updateResult?: {
    success: boolean;
    updatedIngredients: number;
    errors: string[];
  };
}> => {
  const { forceUpdate = false, autoUpdate = true, transaction } = options;

  try {
    // Check if costs are outdated
    const freshnessCheck = await areRecipeCostsOutdated(recipeId, organizationId);
    const isOutdated = freshnessCheck.isOutdated || forceUpdate;

    let wasUpdated = false;
    let updateResult;
    let totalCost = 0;

    // Auto-update if costs are outdated and auto-update is enabled
    if (isOutdated && autoUpdate) {
      try {
        updateResult = await costCalculationService.recalculateRecipeCosts(
          recipeId,
          organizationId,
          transaction
        );

        wasUpdated = updateResult.success;
        totalCost = updateResult.totalCost;

        console.log(`Recipe ${recipeId} costs ${wasUpdated ? 'successfully' : 'partially'} updated. Total cost: ${totalCost}`);
      } catch (error: any) {
        console.error(`Failed to auto-update recipe ${recipeId} costs:`, error);
        updateResult = {
          success: false,
          updatedIngredients: 0,
          errors: [error.message]
        };
      }
    } else if (!isOutdated) {
      // Get current total cost from database
      const costQuery = await sequelize.query(`
        SELECT COALESCE(SUM(ri.ingredient_cost * ri.ingredient_quantity), 0) as total_cost
        FROM mo_recipe_ingredients ri
        WHERE ri.recipe_id = :recipeId 
          AND ri.recipe_ingredient_status = 'active'
      `, {
        replacements: { recipeId },
        type: sequelize.QueryTypes.SELECT,
        transaction
      });

      totalCost = (costQuery[0] as any)?.total_cost || 0;
    }

    return {
      wasOutdated: freshnessCheck.isOutdated,
      wasUpdated,
      totalCost,
      freshness: {
        isOutdated: !wasUpdated && freshnessCheck.isOutdated,
        outdatedIngredients: freshnessCheck.outdatedIngredients,
        lastUpdated: null, // Will be set by calling function
        autoUpdateEnabled: autoUpdate
      },
      updateResult
    };

  } catch (error) {
    console.error(`Error checking and updating recipe costs for recipe ${recipeId}:`, error);
    throw error;
  }
};

/**
 * Handle ingredient cost updates and cascade to recipes
 */
export const handleIngredientCostUpdate = async (
  ingredientId: number,
  organizationId: string,
  options: {
    autoUpdateRecipes?: boolean;
    transaction?: any;
  } = {}
): Promise<{
  success: boolean;
  affectedRecipes: number[];
  updateResults: Array<{
    recipeId: number;
    success: boolean;
    totalCost: number;
    errors: string[];
  }>;
}> => {
  const { autoUpdateRecipes = true, transaction } = options;

  try {
    if (!autoUpdateRecipes) {
      return {
        success: true,
        affectedRecipes: [],
        updateResults: []
      };
    }

    // Auto-update all affected recipes
    const autoUpdateResult = await costCalculationService.autoUpdateRecipeCosts(
      ingredientId,
      organizationId,
      transaction
    );

    // Get detailed results for each recipe
    const updateResults = [];
    for (const recipeId of autoUpdateResult.updatedRecipes) {
      try {
        const recalcResult = await costCalculationService.recalculateRecipeCosts(
          recipeId,
          organizationId,
          transaction
        );

        updateResults.push({
          recipeId,
          success: recalcResult.success,
          totalCost: recalcResult.totalCost,
          errors: recalcResult.errors
        });
      } catch (error: any) {
        updateResults.push({
          recipeId,
          success: false,
          totalCost: 0,
          errors: [error.message]
        });
      }
    }

    return {
      success: autoUpdateResult.success,
      affectedRecipes: autoUpdateResult.updatedRecipes,
      updateResults
    };

  } catch (error) {
    console.error(`Error handling ingredient cost update for ingredient ${ingredientId}:`, error);
    throw error;
  }
};

/**
 * Enhanced freshness check with auto-update capability
 */
export const addFreshnessIndicatorsWithAutoUpdate = async (
  recipe: any,
  organizationId: string,
  options: {
    checkCosts?: boolean;
    checkNutrition?: boolean;
    checkAll?: boolean;
    autoUpdate?: boolean;
    transaction?: any;
  } = { checkAll: true, autoUpdate: true }
): Promise<any> => {
  if (!recipe || !organizationId) return recipe;

  const recipeData = recipe.toJSON ? recipe.toJSON() : recipe;
  const { checkCosts = true, autoUpdate = true, transaction } = options;

  try {
    // Initialize freshness object
    recipeData.freshness = {
      costs: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.ingredient_costs_updated_at,
        checked: checkCosts,
        autoUpdateEnabled: autoUpdate,
        wasAutoUpdated: false
      },
      overallStatus: {
        needsUpdate: false,
        lastChecked: new Date().toISOString(),
        autoUpdateEnabled: autoUpdate
      }
    };

    if (checkCosts) {
      const costCheck = await checkAndUpdateRecipeCosts(
        recipeData.id,
        organizationId,
        { autoUpdate, transaction }
      );

      recipeData.freshness.costs.isOutdated = costCheck.freshness.isOutdated;
      recipeData.freshness.costs.outdatedIngredients = costCheck.freshness.outdatedIngredients;
      recipeData.freshness.costs.wasAutoUpdated = costCheck.wasUpdated;
      recipeData.freshness.costs.totalCost = costCheck.totalCost;

      if (costCheck.updateResult) {
        recipeData.freshness.costs.updateResult = costCheck.updateResult;
      }

      // Update overall status
      recipeData.freshness.overallStatus.needsUpdate = costCheck.freshness.isOutdated;
    }

    return recipeData;

  } catch (error: any) {
    console.error("Error checking recipe freshness with auto-update:", error);

    // Add error indicators
    recipeData.freshness = {
      costs: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.ingredient_costs_updated_at,
        checked: checkCosts,
        autoUpdateEnabled: autoUpdate,
        error: true,
        errorMessage: error.message
      },
      overallStatus: {
        needsUpdate: false,
        lastChecked: new Date().toISOString(),
        autoUpdateEnabled: autoUpdate,
        error: true,
        errorMessage: error.message
      }
    };

    return recipeData;
  }
};

export default {
  checkAndUpdateRecipeCosts,
  handleIngredientCostUpdate,
  addFreshnessIndicatorsWithAutoUpdate
};
