# Recipe Cost Freshness Cron Job

## Overview

This cron job automatically updates recipe ingredient costs based on freshness status. It only updates recipes where automatic cost calculation is enabled (`is_cost_manual = false`) and ingredients are outdated.

## Files

### 1. `recipeCostFreshnessCron.ts`
The main cron job implementation that:
- Finds all organizations with recipes that have automatic cost calculation enabled
- Checks each recipe for outdated ingredient costs using the existing cost-freshness helper
- Updates recipe costs only when ingredients are outdated (`isOutdated = true`)
- Respects the `is_cost_manual` flag (only updates when `false` or `null`)

### 2. `cronScheduler.ts`
The cron scheduler that:
- Initializes and schedules all cron jobs
- Uses the `COST_RECALCULATION_CRON` configuration for scheduling
- Handles timezone settings from configuration

### 3. `cron.routes.ts`
API endpoints for manual testing:
- `POST /v1/private/cron/recipe-cost-freshness` - Manually trigger recipe cost freshness update
- `POST /v1/private/cron/ingredient-cost-update` - Manually trigger ingredient cost update

## Configuration

The cron job uses the following configuration settings from `config.json`:

```json
{
  "COST_RECALCULATION_CRON": "0 * * * *",  // Every hour (default)
  "TIMEZONE": "UTC"
}
```

### Environment-specific schedules:
- **Development**: `0 * * * *` (every hour)
- **Staging**: `0 * * * *` (every hour)  
- **Production**: `0 2 * * *` (daily at 2 AM)

## Functionality

### Primary Function
The cron job calls the existing `checkAndUpdateRecipeCosts` function from the cost-freshness helper, which:
1. Checks if recipe costs are outdated compared to ingredient costs
2. Only updates costs when `isOutdated = true`
3. Updates costs in the recipe ingredient table (`mo_recipe_ingredients`)
4. Updates the recipe cost timestamp (`ingredient_costs_updated_at`)

### Conditional Logic
The cron job only processes recipes where:
- `recipe_status = 'active'`
- `is_cost_manual = false` OR `is_cost_manual IS NULL` (automatic cost calculation enabled)
- The recipe has ingredients with outdated costs

### Scope
- Focuses only on cost calculation and update functionality
- Does not add any extra data or additional features
- Updates costs in the recipe ingredient table only
- Uses existing cost calculation service and helpers

## Usage

### Automatic Execution
The cron job runs automatically based on the configured schedule.

### Manual Execution
You can manually trigger the cron job via API:

```bash
# Update all organizations
POST /v1/private/cron/recipe-cost-freshness
Content-Type: application/json
Authorization: Bearer <token>

{}

# Update specific organization
POST /v1/private/cron/recipe-cost-freshness
Content-Type: application/json
Authorization: Bearer <token>

{
  "organization_id": "your-org-id"
}
```

## Logging

The cron job provides detailed logging:
- Start/completion messages
- Organization processing status
- Recipe update counts
- Error reporting
- Execution time tracking

## Error Handling

- Graceful error handling at organization and recipe levels
- Continues processing other recipes/organizations if one fails
- Comprehensive error logging and reporting
- Returns detailed statistics including error counts

## Integration

The cron job is automatically initialized when the application starts via:
1. Import in `index.ts`
2. Call to `initializeCronJobs()` in the application initialization
3. Automatic scheduling based on configuration

## Dependencies

- Existing cost-freshness helper (`cost-freshness.helper.ts`)
- Cost calculation service (`cost-calculation.service.ts`)
- Recipe and ingredient models
- Sequelize for database operations
- node-cron for scheduling
